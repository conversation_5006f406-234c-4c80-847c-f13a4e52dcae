// MIT/Apache2 License

#ifndef DOZER_UTIL_H
#define DOZER_UTIL_H

#include <stddef.h>
#include <stdint.h>

//
// Basic Utilities
//

// Set the program name.
void U_setname(const char *name);

// Exit the application.
void U_die(const char *fmt, ...);

// Allocate and die if it fails.
void *U_xmalloc(size_t sz);

// Free if non null.
void U_xfree(void *ptr);

//
// Arena Allocation
//

typedef struct U_Arena U_Arena;

// Create a new arena.
U_Arena *U_mkarena(size_t sz);

// Free an arena, along with all memory allocated inside.
void U_freearena(U_Arena *arena);

// Allocate memory from the arena.
void *U_alloc(U_Arena *arena, size_t sz);

// Duplicate a string.
char *U_strdup(U_Arena *arena, const char *str);

//
// Flexible Arrays
//

// Make a new array type.
void *U_mkarray(U_Arena *arena, size_t elem_size, size_t capacity);

// Free the array.
void U_freearray(void *array);

// Grow the array.
void *U_expand(void *array, size_t new_len);

// Append the memory to the array.
void *U_addmem(void *array, const void *data, size_t len);

// Get the length of the array.
size_t U_len(void *array);

// Reduce the size of an array.
void U_shrink(void *array, size_t subtracted);

// Clear an array.
void U_clear(void *array);

#define MIN(a, b) ((a) < (b) ? (a) : (b))
#define MAX(a, b) ((a) > (b) ? (a) : (b))

#endif
