// MIT/Apache2 License

#include "../utils/util.h"
#include "internal.h"

#include <stdarg.h>
#include <string.h>

// Parser interface.
struct P_Parser {
  U_Arena *arena;
  T_Token *tokens;
  size_t cursor;

  enum { PS_NORMAL, PS_ERROR } state;
  char errmsg[256];
};

// Printer interface
struct P_Printer {
  T_Token *tokens;
  U_Arena *arena;
  T_Edition edition;
};

P_Parser *P_new(L_Lexer *lexer) {
  P_Parser *parser;
  T_Token token;

  parser = U_xmalloc(sizeof(P_Parser));
  parser->arena = U_mkarena(1024);
  parser->cursor = 0;
  parser->tokens = U_mkarray(NULL, sizeof(T_Token), 4096);
  parser->state = PS_NORMAL;
  memset(parser->errmsg, 0, sizeof(parser->errmsg));

  // Read from the lexer.
  do {
    L_lex(lexer, &token);
    U_addmem(&parser->tokens, &token, 1);
  } while (token.kind != TK_EOF);

  return parser;
}

const T_Token *P_cur(const P_Parser *parser) {
  return &parser->tokens[parser->cursor];
}

const T_Token *P_peek(const P_Parser *parser) {
  const T_Token *cur;

  cur = P_cur(parser);
  if (cur->kind == TK_EOF)
    return cur;
  return &parser->tokens[parser->cursor + 1];
}

const T_Token *P_peek2(const P_Parser *parser) {
  const T_Token *cur;

  cur = P_peek(parser);
  if (cur->kind == TK_EOF)
    return cur;
  return &parser->tokens[parser->cursor + 2];
}

const T_Token *P_peek3(const P_Parser *parser) {
  const T_Token *cur;

  cur = P_peek2(parser);
  if (cur->kind == TK_EOF)
    return cur;
  return &parser->tokens[parser->cursor + 3];
}

void P_next(P_Parser *parser) {
  if (P_cur(parser)->kind == TK_EOF)
    return;
  parser->cursor++;
}

bool P_consume(P_Parser *parser, T_Kind kind) {
  if (P_cur(parser)->kind != kind)
    return false;
  P_next(parser);
  return true;
}

char *P_expect(P_Parser *parser, T_Kind kind) {
  const T_Token *cur;

  cur = P_cur(parser);
  if (P_consume(parser, kind))
    return cur->literal;

  P_error(parser, "expected %s, found %s", T_desc(kind), T_desc(cur->kind));
  return NULL;
}

void P_error(P_Parser *parser, const char *fmt, ...) {
  va_list ap;

  parser->state = PS_ERROR;
  va_start(ap, fmt);
  vsnprintf(parser->errmsg, sizeof(parser->errmsg), fmt, ap);
  va_end(ap);

  parser->errmsg[sizeof(parser->errmsg) - 1] = '\0';
}

void P_bail(P_Parser *parser) {
  if (parser->state == PS_NORMAL)
    return;
  T_error(&P_cur(parser)->loc, "%s", parser->errmsg);
}

void P_clear(P_Parser *parser) {
  parser->state = PS_NORMAL;
  memset(parser->errmsg, 0, sizeof(parser->errmsg));
}

U_Arena *P_arena(const P_Parser *parser) { return parser->arena; }

void *P_alloc(P_Parser *parser, size_t sz) {
  return U_alloc(parser->arena, sz);
}

char *P_strdup(P_Parser *parser, const char *str) {
  return U_strdup(parser->arena, str);
}

void P_pushtok(P_Printer *printer, T_Kind kind, T_Loc loc, const char *lit) {
  T_Token tok;

  tok.kind = kind;
  tok.loc = loc;
  tok.literal = U_strdup(printer->arena, lit);

  T_keyword(&tok, printer->edition);
  U_addmem(&printer->tokens, &tok, 1);
}
