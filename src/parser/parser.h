// MIT/Apache2 License

#ifndef DOZER_PARSER_H
#define DOZER_PARSER_H

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

// Location of a token.
typedef struct T_Loc {
  const char *source_file;
  size_t line, column;
} T_Loc;

/* Token Kind */
// clang-format off
#define TOKEN_KINDS(X) \
  X(NONE,                    "", NOK) \
  X(EOF,                     "", NOK) \
  X(IDENT,                   "", NOK) \
  X(INTEGER,                 "", NOK) \
  X(FLOAT,                   "", NOK) \
  X(LIFETIME,                "", NOK) \
  X(CHAR_LITERAL,            "", NOK) \
  X(BYTE_LITERAL,            "", NOK) \
  X(STRING_LITERAL,          "", NOK) \
  X(RAW_STRING_LITERAL,      "", NOK) \
  X(BYTE_STRING_LITERAL,     "", NOK) \
  X(RAW_BYTE_STRING_LITERAL, "", NOK) \
  X(C_STRING_LITERAL,        "", NOK) \
  X(RAW_C_STRING_LITERAL,    "", NOK) \
  \
  X(PLUS,      "+",   NOK) \
  X(MINUS,     "-",   NOK) \
  X(STAR,      "*",   NOK) \
  X(SLASH,     "/",   NOK) \
  X(PERCENT,   "%",   NOK) \
  X(CARET,     "^",   NOK) \
  X(NOT,       "!",   NOK) \
  X(AND,       "&",   NOK) \
  X(OR,        "|",   NOK) \
  X(ANDAND,    "&&",  NOK) \
  X(OROR,      "||",  NOK) \
  X(SHL,       "<<",  NOK) \
  X(SHR,       ">>",  NOK) \
  X(PLUSEQ,    "+=",  NOK) \
  X(MINUSEQ,   "-=",  NOK) \
  X(STAREQ,    "*=",  NOK) \
  X(SLASHEQ,   "/=",  NOK) \
  X(PERCENTEQ, "%=",  NOK) \
  X(CARETEQ,   "^=",  NOK) \
  X(ANDEQ,     "&=",  NOK) \
  X(OREQ,      "|=",  NOK) \
  X(SHLEQ,     "<<=", NOK) \
  X(SHREQ,     ">>=", NOK) \
  \
  X(EQ,        "=",  NOK) \
  X(EQEQ,      "==", NOK) \
  X(NEQ,       "!=", NOK) \
  X(LT,        "<",  NOK) \
  X(LE,        "<=", NOK) \
  X(GT,        ">",  NOK) \
  X(GE,        ">=", NOK) \
  \
  X(AT,         "@",   NOK) \
  X(UNDERSCORE, "_",   NOK) \
  X(DOT,        ".",   NOK) \
  X(DOTDOT,     "..",  NOK) \
  X(DOTDOTEQ,   "..=", NOK) \
  X(DOTDOTDOT,  "...", NOK) \
  X(COMMA,      ",",   NOK) \
  X(SEMI,       ";",   NOK) \
  X(COLON,      ":",   NOK) \
  X(PATHSEP,    "::",  NOK) \
  X(RARROW,     "->",  NOK) \
  X(FATARROW,   "=>",  NOK) \
  X(LARROW,     "<-",  NOK) \
  X(POUND,      "#",   NOK) \
  X(DOLLAR,     "$",   NOK) \
  X(QUESTION,   "?",   NOK) \
  X(TILDE,      "~",   NOK) \
  \
  X(LBRACE,     "{",   NOK | GROUP) \
  X(RBRACE,     "}",   NOK | GROUP) \
  X(LBRACKET,   "[",   NOK | GROUP) \
  X(RBRACKET,   "]",   NOK | GROUP) \
  X(LPAREN,     "(",   NOK | GROUP) \
  X(RPAREN,     ")",   NOK | GROUP) \
  X(LNONE,      "",    NOK | GROUP) \
  X(RNONE,      "",    NOK | GROUP) \
  \
  X(AS,        "as",       STRICT) \
  X(BREAK,     "break",    STRICT) \
  X(CONST,     "const",    STRICT) \
  X(CONTINUE,  "continue", STRICT) \
  X(CRATE,     "crate",    STRICT) \
  X(ELSE,      "else",     STRICT) \
  X(ENUM,      "enum",     STRICT) \
  X(EXTERN,    "extern",   STRICT) \
  X(FALSE,     "false",    STRICT) \
  X(FN,        "fn",       STRICT) \
  X(FOR,       "for",      STRICT) \
  X(IF,        "if",       STRICT) \
  X(IMPL,      "impl",     STRICT) \
  X(IN,        "in",       STRICT) \
  X(LET,       "let",      STRICT) \
  X(LOOP,      "loop",     STRICT) \
  X(MATCH,     "match",    STRICT) \
  X(MOD,       "mod",      STRICT) \
  X(MUT,       "mut",      STRICT) \
  X(PUB,       "pub",      STRICT) \
  X(REF,       "ref",      STRICT) \
  X(RETURN,    "return",   STRICT) \
  X(SELFVALUE, "self",     STRICT) \
  X(SELFTYPE,  "Self",     STRICT) \
  X(STATIC,    "static",   STRICT) \
  X(STRUCT,    "struct",   STRICT) \
  X(SUPER,     "super",    STRICT) \
  X(TRAIT,     "trait",    STRICT) \
  X(TRUE,      "true",     STRICT) \
  X(TYPE,      "type",     STRICT) \
  X(UNSAFE,    "unsafe",   STRICT) \
  X(USE,       "use",      STRICT) \
  X(WHERE,     "where",    STRICT) \
  X(WHILE,     "while",    STRICT) \
  \
  X(ASYNC, "async", STRICT | E_2018) \
  X(AWAIT, "await", STRICT | E_2018) \
  X(DYN,   "dyn",   STRICT | E_2018) \
  \
  X(ABSTRACT, "abstract", RESERVED) \
  X(BECOME,   "become",   RESERVED) \
  X(BOX,      "box",      RESERVED) \
  X(DO,       "do",       RESERVED) \
  X(FINAL,    "final",    RESERVED) \
  X(MACRO,    "macro",    RESERVED) \
  X(OVVERIDE, "override", RESERVED) \
  X(PRIV,     "priv",     RESERVED) \
  X(TYPEOF,   "typeof",   RESERVED) \
  X(UNSIZED,  "unsized",  RESERVED) \
  X(VIRTUAL,  "virtual",  RESERVED) \
  X(YIELD,    "yield",    RESERVED) \
  \
  X(TRY, "try", RESERVED | E_2018) \
  \
  X(MACRO_RULES,     "macro_rules", WEAK) \
  X(UNION,           "union", WEAK) \
  X(STATIC_LIFETIME, "'static", WEAK)
// clang-format on

typedef enum {
#define X(name, str, flags) TK_##name,
  TOKEN_KINDS(X)
#undef X
} T_Kind;

// Token in the source file.
typedef struct T_Token {
  T_Kind kind;
  T_Loc loc;
  char *literal;
} T_Token;

typedef struct E_Expr E_Expr;
typedef struct ST_Block ST_Block;
typedef struct TY_Type TY_Type;

// A single path segment.
typedef struct PT_Segment {
  enum { PS_NONE = 0, PS_ANGLE_BRACKETED, PS_PARENTHESIZED } kind;

  T_Loc loc;
  char *ident;
} PT_Segment;

// Path in Rust code.
typedef struct PT_Path {
  T_Loc loc;
  bool has_leading_colon;
  PT_Segment *segments;
} PT_Path;

// Explicit "self" type in a qualified path.
typedef struct PT_QSelf {
  T_Loc loc;
  TY_Type *type;
  int position;
  bool has_as_token;
} PT_QSelf;

// Attribute in Rust code.
typedef struct AT_Attribute {
  enum { AK_PATH = 0, AK_LIST, AK_NAME_VALUE } kind;
  T_Loc loc;
  enum { AS_INNER, AS_OUTER } style;

  PT_Path path;
  union {
    E_Expr *expr;
    struct {
      T_Token *tokens;
    };
  };
} AT_Attribute;

enum {
  EML_CONST = 0x1,
  EML_MUT = 0x2,

  ELT_STR = 0,
  ELT_BYTE_STR,
  ELT_C_STR,
  ELT_BYTE,
  ELT_CHAR,
  ELT_INT,
  ELT_FLOAT,
  ELT_BOOL
};

// Expression precedence levels
typedef enum {
  PREC_JUMP = 0,     // return, break, closures
  PREC_ASSIGN,       // = += -= *= /= %= &= |= ^= <<= >>=
  PREC_RANGE,        // .. ..=
  PREC_OR,           // ||
  PREC_AND,          // &&
  PREC_LET,          // let
  PREC_COMPARE,      // == != < > <= >=
  PREC_BIT_OR,       // |
  PREC_BIT_XOR,      // ^
  PREC_BIT_AND,      // &
  PREC_SHIFT,        // << >>
  PREC_SUM,          // + -
  PREC_PRODUCT,      // * / %
  PREC_CAST,         // as
  PREC_PREFIX,       // unary - * ! & &mut
  PREC_UNAMBIGUOUS,  // paths, loops, function calls, array indexing, field expressions, method calls
} E_Precedence;

#define PREC_MIN PREC_JUMP

// Member access (field or tuple index)
typedef struct E_Member {
  enum { EM_NAMED, EM_UNNAMED } kind;
  union {
    char *name;
    int index;
  };
} E_Member;

// Expression in Rust code.
struct E_Expr {
  enum {
    EK_NONE = 0,

    EK_ARRAY,
    EK_ASSIGN,
    EK_ASYNC,
    EK_AWAIT,
    EK_BINARY,
    EK_BLOCK,
    EK_BREAK,
    EK_CALL,
    EK_CAST,
    EK_CLOSURE,
    EK_CONST,
    EK_CONTINUE,
    EK_FIELD,
    EK_FOR_LOOP,
    EK_GROUP,
    EK_IF,
    EK_INDEX,
    EK_INFER,
    EK_LET,
    EK_LIT,
    EK_LOOP,
    EK_MACRO,
    EK_MATCH,
    EK_METHOD_CALL,
    EK_PAREN,
    EK_PATH,
    EK_RANGE,
    EK_RAW_ADDR,
    EK_REFERENCE,
    EK_REPEAT,
    EK_RETURN,
    EK_STRUCT,
    EK_TRY,
    EK_TRY_BLOCK,
    EK_TUPLE,
    EK_UNARY,
    EK_UNSAFE,
    EK_WHILE,
    EK_YIELD
  } kind;

  T_Loc loc;
  AT_Attribute *attrs;
  E_Expr *left, *right, **children;
  char *literal;
  int flags;
  union {
    ST_Block *block;
    TY_Type *ty;
    enum { ERL_HALF_OPEN = 0, ERL_CLOSED } range_limits;
    struct {
      PT_QSelf *qself;
      PT_Path path;
    };
    T_Kind op;
    E_Member member;
    char *ident;
    char *lifetime;
  };
};

// Statement in Rust code.
typedef struct ST_Stmt {
  enum { SK_LOCAL = 0, SK_ITEM, SK_EXPR, SK_MACRO } kind;

  E_Expr *expr;
} ST_Stmt;

// Block of Rust statements.
struct ST_Block {
  T_Loc loc;
  ST_Stmt *stmts;
};

// Type in Rust.
struct TY_Type {
  enum {
    TY_ARRAY = 0,
    TY_BARE_FN,
    TY_GROUP,
    TY_IMPL_TRAIT,
    TY_INFER,
    TY_MACRO,
    TY_NEVER,
    TY_PAREN,
    TY_PATH,
    TY_PTR,
    TY_REFERENCE,
    TY_SLICE,
    TY_TRAIT_OBJECT,
    TY_TUPLE
  } kind;

  TY_Type *inner, **subtypes;
  enum { TYIS_CONST = 0x1, TYIS_MUT = 0x2 } flags;
  union {
    E_Expr *expr;
    PT_Path path;
    char *lifetime;
  };
};

#endif
