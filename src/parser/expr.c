// MIT/Apache2 License

#include "../utils/util.h"
#include "internal.h"

#include <string.h>
#include <stdlib.h>
#include <stdio.h>

// Forward declarations
static E_Expr *ambiguous_expr(P_Parser *parser, bool allow_struct);
static E_Expr *trailer_helper(P_Parser *parser, E_Expr *expr);
static E_Expr *parse_expr(P_Parser *parser, E_Expr *lhs, bool allow_struct, E_Precedence min_prec);
static E_Expr *array_or_repeat(P_Parser *parser);
static E_Expr *expr_break(P_Parser *parser, bool allow_struct);
static E_Expr *expr_continue(P_Parser *parser);
static E_Expr *expr_return(P_Parser *parser, bool allow_struct);
static E_Expr *expr_if(P_Parser *parser);
static E_Expr *expr_while(P_Parser *parser);
static E_Expr *expr_for_loop(P_Parser *parser);
static E_Expr *expr_loop(P_Parser *parser);
static E_Expr *expr_match(P_Parser *parser);
static E_Expr *expr_unsafe(P_Parser *parser);
static E_Expr *expr_block(P_Parser *parser);
static E_Expr *expr_range(P_Parser *parser, bool allow_struct);
static E_Expr *expr_infer(P_Parser *parser);
static E_Expr *expr_let(P_Parser *parser, bool allow_struct);
static E_Expr *expr_yield(P_Parser *parser, bool allow_struct);
static E_Expr *expr_const(P_Parser *parser);
static E_Expr *expr_closure(P_Parser *parser);
static E_Expr *expr_async(P_Parser *parser);

// Create a new empty expression.
static E_Expr *enew(P_Parser *parser, AT_Attribute *attrs, int kind) {
  E_Expr *expr;

  expr = P_alloc(parser, sizeof(*expr));
  memset(expr, 0, sizeof(*expr));
  expr->kind = kind;
  expr->attrs = attrs;

  return expr;
}

// Parse attributes.
static AT_Attribute *attributes(P_Parser *parser) {
  // Basic attribute parsing - just skip them for now
  // TODO: Implement proper attribute parsing
  while (CUR->kind == TK_POUND) {
    P_next(parser); // consume '#'
    if (P_consume(parser, TK_NOT)) {
      // Inner attribute #![...]
    }
    if (P_consume(parser, TK_LBRACKET)) {
      // Skip to closing bracket
      int depth = 1;
      while (depth > 0 && CUR->kind != TK_EOF) {
        if (CUR->kind == TK_LBRACKET) depth++;
        else if (CUR->kind == TK_RBRACKET) depth--;
        P_next(parser);
      }
    }
  }
  return NULL; // Return NULL for now since we're not storing attributes
}

// Literal expression.
static E_Expr *expr_lit(P_Parser *parser) {
  E_Expr *expr;
  int kind;

  switch (CUR->kind) {
  case TK_STRING_LITERAL:
  case TK_RAW_STRING_LITERAL:
    kind = ELT_STR;
    break;
  case TK_BYTE_STRING_LITERAL:
  case TK_RAW_BYTE_STRING_LITERAL:
    kind = ELT_BYTE_STR;
    break;
  case TK_C_STRING_LITERAL:
  case TK_RAW_C_STRING_LITERAL:
    kind = ELT_C_STR;
    break;
  case TK_BYTE_LITERAL:
    kind = ELT_BYTE;
    break;
  case TK_CHAR_LITERAL:
    kind = ELT_CHAR;
    break;
  case TK_INTEGER:
    kind = ELT_INT;
    break;
  case TK_FLOAT:
    kind = ELT_FLOAT;
    break;
  case TK_TRUE:
  case TK_FALSE:
    kind = ELT_BOOL;
    break;
  default:
    P_error(parser, "expected literal, found %s", T_desc(CUR->kind));
    return NULL;
  }

  expr = enew(parser, NULL, EK_LIT);
  expr->literal = P_strdup(parser, CUR->literal);
  expr->flags = kind;
  P_next(parser);

  return expr;
}

// Get precedence of a binary operator token
E_Precedence E_precedence_of_binop(T_Kind op) {
  switch (op) {
  case TK_PLUS:
  case TK_MINUS:
    return PREC_SUM;
  case TK_STAR:
  case TK_SLASH:
  case TK_PERCENT:
    return PREC_PRODUCT;
  case TK_ANDAND:
    return PREC_AND;
  case TK_OROR:
    return PREC_OR;
  case TK_CARET:
    return PREC_BIT_XOR;
  case TK_AND:
    return PREC_BIT_AND;
  case TK_OR:
    return PREC_BIT_OR;
  case TK_SHL:
  case TK_SHR:
    return PREC_SHIFT;
  case TK_EQEQ:
  case TK_LT:
  case TK_LE:
  case TK_NEQ:
  case TK_GE:
  case TK_GT:
    return PREC_COMPARE;
  case TK_EQ:
  case TK_PLUSEQ:
  case TK_MINUSEQ:
  case TK_STAREQ:
  case TK_SLASHEQ:
  case TK_PERCENTEQ:
  case TK_CARETEQ:
  case TK_ANDEQ:
  case TK_OREQ:
  case TK_SHLEQ:
  case TK_SHREQ:
    return PREC_ASSIGN;
  case TK_DOTDOT:
  case TK_DOTDOTEQ:
    return PREC_RANGE;
  case TK_AS:
    return PREC_CAST;
  default:
    return PREC_UNAMBIGUOUS;
  }
}

// Parse a structure instantiation.
static E_Expr *expr_struct_helper(P_Parser *parser, PT_QSelf *qself,
                                  PT_Path path) {
  E_Expr *expr;

  expr = enew(parser, NULL, EK_STRUCT);
  expr->qself = qself;
  expr->path = path;

  // TODO: Parse struct fields
  EXPECT(TK_LBRACE);
  EXPECT(TK_RBRACE);

  return expr;
}

static E_Expr *rest_of_path_or_macro_or_struct(P_Parser *parser,
                                               PT_QSelf *qself, PT_Path path,
                                               bool allow_struct) {
  E_Expr *expr;

  if (!qself && CUR->kind == TK_NOT && PEEK->kind != TK_EQ &&
      !PT_ismodstyle(&path)) {
    U_die("TODO: macro");
  }

  if (allow_struct && CUR->kind == TK_LBRACE) {
    return expr_struct_helper(parser, qself, path);
  }

  expr = enew(parser, NULL, EK_PATH);
  expr->qself = qself;
  expr->path = path;
  return expr;
}

// A path, a macro or a struct.
static E_Expr *path_or_macro_or_struct(P_Parser *parser, bool allow_struct) {
  PT_QSelf *qself;
  PT_Path path;

  if (!P_qpath(parser, true, &path, &qself))
    return NULL;

  return rest_of_path_or_macro_or_struct(parser, qself, path, allow_struct);
}

// Parse parenthesized expression or tuple
static E_Expr *paren_or_tuple(P_Parser *parser, bool allow_struct) {
  E_Expr *expr;

  EXPECT(TK_LPAREN);

  // Empty tuple: ()
  if (P_consume(parser, TK_RPAREN)) {
    expr = enew(parser, NULL, EK_TUPLE);
    expr->children = U_mkarray(P_arena(parser), sizeof(E_Expr*), 0);
    return expr;
  }

  // Parse first expression
  expr = ambiguous_expr(parser, allow_struct);
  if (!expr)
    return NULL;

  // Check if it's a tuple (has comma) or just parentheses
  if (P_consume(parser, TK_COMMA)) {
    E_Expr *tuple_expr = enew(parser, NULL, EK_TUPLE);
    tuple_expr->children = U_mkarray(P_arena(parser), sizeof(E_Expr*), 4);
    U_addmem(&tuple_expr->children, &expr, 1);

    // Parse remaining tuple elements
    while (CUR->kind != TK_RPAREN) {
      E_Expr *elem = ambiguous_expr(parser, allow_struct);
      if (!elem)
        return NULL;
      U_addmem(&tuple_expr->children, &elem, 1);

      if (!P_consume(parser, TK_COMMA))
        break;
    }

    EXPECT(TK_RPAREN);
    return tuple_expr;
  } else {
    // Just parentheses around expression
    EXPECT(TK_RPAREN);
    E_Expr *paren_expr = enew(parser, NULL, EK_PAREN);
    paren_expr->left = expr;
    return paren_expr;
  }
}

// Atomic expression.
static E_Expr *atom_expr(P_Parser *parser, bool allow_struct) {
  E_Expr *expr;

  switch (CUR->kind) {
  case TK_LNONE:
    U_die("TODO: groups");
  case TK_STRING_LITERAL:
  case TK_RAW_STRING_LITERAL:
  case TK_BYTE_STRING_LITERAL:
  case TK_RAW_BYTE_STRING_LITERAL:
  case TK_C_STRING_LITERAL:
  case TK_RAW_C_STRING_LITERAL:
  case TK_BYTE_LITERAL:
  case TK_CHAR_LITERAL:
  case TK_INTEGER:
  case TK_FLOAT:
  case TK_TRUE:
  case TK_FALSE:
    return expr_lit(parser);
  case TK_ASYNC:
    return expr_async(parser);
  case TK_TRY:
    U_die("TODO: try block");
  case TK_OR:
    return expr_closure(parser);
  case TK_IDENT:
  case TK_PATHSEP:
  case TK_LT:
  case TK_SELFTYPE:
  case TK_SELFVALUE:
  case TK_SUPER:
  case TK_CRATE:
    return path_or_macro_or_struct(parser, allow_struct);
  case TK_LPAREN:
    return paren_or_tuple(parser, allow_struct);
  case TK_LBRACKET:
    return array_or_repeat(parser);
  case TK_BREAK:
    return expr_break(parser, allow_struct);
  case TK_CONTINUE:
    return expr_continue(parser);
  case TK_RETURN:
    return expr_return(parser, allow_struct);
  case TK_IF:
    return expr_if(parser);
  case TK_WHILE:
    return expr_while(parser);
  case TK_FOR:
    return expr_for_loop(parser);
  case TK_LOOP:
    return expr_loop(parser);
  case TK_MATCH:
    return expr_match(parser);
  case TK_UNSAFE:
    return expr_unsafe(parser);
  case TK_LBRACE:
    return expr_block(parser);
  case TK_DOTDOT:
  case TK_DOTDOTEQ:
    return expr_range(parser, allow_struct);
  case TK_UNDERSCORE:
    return expr_infer(parser);
  case TK_LET:
    return expr_let(parser, allow_struct);
  case TK_YIELD:
    return expr_yield(parser, allow_struct);
  case TK_CONST:
    return expr_const(parser);
  default:
    P_error(parser, "expected expression, found %s", T_desc(CUR->kind));
    return NULL;
  }
}

// Expression with a trailer after it (like a dot).
static E_Expr *trailer_expr(P_Parser *parser, AT_Attribute *attrs,
                            bool allow_struct) {
  E_Expr *expr;

  expr = atom_expr(parser, allow_struct);
  if (!expr)
    return NULL;
  expr = trailer_helper(parser, expr);
  if (!expr)
    return NULL;

  if (attrs) {
    // TODO
  }

  return expr;
}

// Parse a unary expression:
static E_Expr *unary_expr(P_Parser *parser, bool allow_struct) {
  AT_Attribute *attrs;
  E_Expr *expr;
  bool is_raw, is_mut;

  attrs = attributes(parser);

  switch (CUR->kind) {
  case TK_AND:
    // Reference or raw pointer
    P_next(parser);

    // Check for raw pointer: &raw const or &raw mut
    if (CUR->kind == TK_IDENT && strcmp(CUR->literal, "raw") == 0) {
      P_next(parser); // consume "raw"

      expr = enew(parser, attrs, EK_RAW_ADDR);
      if (P_consume(parser, TK_CONST)) {
        expr->flags = EML_CONST;
      } else if (P_consume(parser, TK_MUT)) {
        expr->flags = EML_MUT;
      } else {
        P_error(parser, "expected 'const' or 'mut' after 'raw'");
        return NULL;
      }
      expr->left = unary_expr(parser, allow_struct);
      if (!expr->left)
        return NULL;
      return expr;
    } else {
      // Regular reference
      expr = enew(parser, attrs, EK_REFERENCE);
      expr->flags = P_consume(parser, TK_MUT) ? EML_MUT : 0;
      expr->left = unary_expr(parser, allow_struct);
      if (!expr->left)
        return NULL;
      return expr;
    }
  case TK_STAR:
  case TK_NOT:
  case TK_MINUS:
    expr = enew(parser, attrs, EK_UNARY);
    expr->op = CUR->kind;
    P_next(parser);
    expr->left = unary_expr(parser, allow_struct);
    if (!expr->left)
      return NULL;
    return expr;
  case TK_NONE:
  default:
    return trailer_expr(parser, attrs, allow_struct);
  }
}

// Parse an arbitary expression.
static E_Expr *ambiguous_expr(P_Parser *parser, bool allow_struct) {
  E_Expr *expr;

  expr = unary_expr(parser, allow_struct);
  if (!expr)
    return NULL;

  return parse_expr(parser, expr, allow_struct, PREC_MIN);
}

E_Expr *E_parse(P_Parser *parser) {
  return ambiguous_expr(parser, true);
}

// Parse binary expressions with precedence
static E_Expr *parse_expr(P_Parser *parser, E_Expr *lhs, bool allow_struct, E_Precedence min_prec) {
  while (1) {
    T_Kind op = CUR->kind;
    E_Precedence prec = E_precedence_of_binop(op);

    if (prec < min_prec)
      break;

    P_next(parser); // consume operator

    E_Expr *rhs = unary_expr(parser, allow_struct);
    if (!rhs)
      return NULL;

    // Handle right-associative operators and higher precedence
    T_Kind next_op = CUR->kind;
    E_Precedence next_prec = E_precedence_of_binop(next_op);

    if (prec < next_prec || (prec == next_prec && prec == PREC_ASSIGN)) {
      rhs = parse_expr(parser, rhs, allow_struct, prec + 1);
      if (!rhs)
        return NULL;
    }

    // Create appropriate expression type
    E_Expr *result;
    if (op == TK_EQ) {
      // Assignment expression
      result = enew(parser, NULL, EK_ASSIGN);
      result->left = lhs;
      result->right = rhs;
    } else if (op == TK_AS) {
      // Cast expression - we already consumed the 'as' token
      result = enew(parser, NULL, EK_CAST);
      result->left = lhs;
      // For cast, we need to parse a type, not an expression
      result->ty = TY_parse(parser);
      if (!result->ty)
        return NULL;
    } else if (prec == PREC_ASSIGN) {
      // Compound assignment (+=, -=, etc.)
      result = enew(parser, NULL, EK_BINARY);
      result->left = lhs;
      result->right = rhs;
      result->op = op;
    } else {
      // Regular binary expression
      result = enew(parser, NULL, EK_BINARY);
      result->left = lhs;
      result->right = rhs;
      result->op = op;
    }
    lhs = result;
  }

  return lhs;
}

// Handle trailers like function calls, field access, indexing
static E_Expr *trailer_helper(P_Parser *parser, E_Expr *expr) {
  while (1) {
    switch (CUR->kind) {
    case TK_LPAREN: {
      // Function call
      E_Expr *call = enew(parser, NULL, EK_CALL);
      call->left = expr;
      call->children = U_mkarray(P_arena(parser), sizeof(E_Expr*), 4);

      P_next(parser); // consume '('

      while (CUR->kind != TK_RPAREN) {
        E_Expr *arg = ambiguous_expr(parser, true);
        if (!arg)
          return NULL;
        U_addmem(&call->children, &arg, 1);

        if (!P_consume(parser, TK_COMMA))
          break;
        // Allow trailing comma
        if (CUR->kind == TK_RPAREN)
          break;
      }

      EXPECT(TK_RPAREN);
      expr = call;
      break;
    }
    case TK_DOT: {
      // Field access or method call
      P_next(parser); // consume '.'

      if (CUR->kind == TK_AWAIT) {
        // Await expression
        E_Expr *await_expr = enew(parser, NULL, EK_AWAIT);
        await_expr->left = expr;
        P_next(parser);
        expr = await_expr;
      } else if (CUR->kind == TK_IDENT) {
        char *method_name = P_strdup(parser, CUR->literal);
        P_next(parser);

        // Check if this is a method call (has parentheses after the identifier)
        // or if it has turbofish syntax (::<>)
        bool is_method_call = false;

        // Check for turbofish syntax
        if (CUR->kind == TK_PATHSEP && PEEK->kind == TK_LT) {
          // This is a method call with generic arguments
          is_method_call = true;
          // TODO: Parse turbofish generics
          P_next(parser); // consume '::'
          P_next(parser); // consume '<'
          // Skip to matching '>'
          int depth = 1;
          while (depth > 0 && CUR->kind != TK_EOF) {
            if (CUR->kind == TK_LT) depth++;
            else if (CUR->kind == TK_GT) depth--;
            P_next(parser);
          }
        }

        // Check for method call parentheses
        if (CUR->kind == TK_LPAREN) {
          is_method_call = true;
        }

        if (is_method_call) {
          // Method call
          E_Expr *method = enew(parser, NULL, EK_METHOD_CALL);
          method->left = expr; // receiver
          method->ident = method_name;
          method->children = U_mkarray(P_arena(parser), sizeof(E_Expr*), 4);

          if (CUR->kind == TK_LPAREN) {
            P_next(parser); // consume '('

            while (CUR->kind != TK_RPAREN) {
              E_Expr *arg = ambiguous_expr(parser, true);
              if (!arg)
                return NULL;
              U_addmem(&method->children, &arg, 1);

              if (!P_consume(parser, TK_COMMA))
                break;
              // Allow trailing comma
              if (CUR->kind == TK_RPAREN)
                break;
            }

            EXPECT(TK_RPAREN);
          }

          expr = method;
        } else {
          // Field access
          E_Expr *field = enew(parser, NULL, EK_FIELD);
          field->left = expr;
          field->member.kind = EM_NAMED;
          field->member.name = method_name;
          expr = field;
        }
      } else if (CUR->kind == TK_INTEGER) {
        E_Expr *field = enew(parser, NULL, EK_FIELD);
        field->left = expr;
        field->member.kind = EM_UNNAMED;
        field->member.index = atoi(CUR->literal);
        P_next(parser);
        expr = field;
      } else {
        P_error(parser, "expected field name or index after '.'");
        return NULL;
      }
      break;
    }
    case TK_LBRACKET: {
      // Array indexing
      E_Expr *index = enew(parser, NULL, EK_INDEX);
      index->left = expr;

      P_next(parser); // consume '['
      index->right = ambiguous_expr(parser, true);
      if (!index->right)
        return NULL;
      EXPECT(TK_RBRACKET);

      expr = index;
      break;
    }
    case TK_QUESTION: {
      // Try operator
      E_Expr *try_expr = enew(parser, NULL, EK_TRY);
      try_expr->left = expr;
      P_next(parser);
      expr = try_expr;
      break;
    }
    default:
      return expr;
    }
  }
}

// Parse array literal or repeat expression
static E_Expr *array_or_repeat(P_Parser *parser) {
  E_Expr *expr;

  EXPECT(TK_LBRACKET);

  // Empty array
  if (P_consume(parser, TK_RBRACKET)) {
    expr = enew(parser, NULL, EK_ARRAY);
    expr->children = U_mkarray(P_arena(parser), sizeof(E_Expr*), 0);
    return expr;
  }

  // Parse first expression
  E_Expr *first = ambiguous_expr(parser, true);
  if (!first)
    return NULL;

  if (P_consume(parser, TK_SEMI)) {
    // Repeat expression [expr; count]
    expr = enew(parser, NULL, EK_REPEAT);
    expr->left = first;
    expr->right = ambiguous_expr(parser, true);
    if (!expr->right)
      return NULL;
  } else {
    // Array expression [expr, ...]
    expr = enew(parser, NULL, EK_ARRAY);
    expr->children = U_mkarray(P_arena(parser), sizeof(E_Expr*), 4);
    U_addmem(&expr->children, &first, 1);

    while (P_consume(parser, TK_COMMA)) {
      if (CUR->kind == TK_RBRACKET)
        break;
      E_Expr *elem = ambiguous_expr(parser, true);
      if (!elem)
        return NULL;
      U_addmem(&expr->children, &elem, 1);
    }
  }

  EXPECT(TK_RBRACKET);
  return expr;
}

// Parse break expression
static E_Expr *expr_break(P_Parser *parser, bool allow_struct) {
  E_Expr *expr = enew(parser, NULL, EK_BREAK);

  EXPECT(TK_BREAK);

  // Optional lifetime label
  if (CUR->kind == TK_LIFETIME) {
    expr->lifetime = P_strdup(parser, CUR->literal);
    P_next(parser);
  }

  // Optional break value
  if (CUR->kind != TK_SEMI && CUR->kind != TK_RBRACE && CUR->kind != TK_EOF) {
    expr->left = ambiguous_expr(parser, allow_struct);
  }

  return expr;
}

// Parse continue expression
static E_Expr *expr_continue(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_CONTINUE);

  EXPECT(TK_CONTINUE);

  // Optional lifetime label
  if (CUR->kind == TK_LIFETIME) {
    expr->lifetime = P_strdup(parser, CUR->literal);
    P_next(parser);
  }

  return expr;
}

// Parse return expression
static E_Expr *expr_return(P_Parser *parser, bool allow_struct) {
  E_Expr *expr = enew(parser, NULL, EK_RETURN);

  EXPECT(TK_RETURN);

  // Optional return value
  if (CUR->kind != TK_SEMI && CUR->kind != TK_RBRACE && CUR->kind != TK_EOF) {
    expr->left = ambiguous_expr(parser, allow_struct);
  }

  return expr;
}

// Parse infer expression (_)
static E_Expr *expr_infer(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_INFER);
  EXPECT(TK_UNDERSCORE);
  return expr;
}

// Parse if expression
static E_Expr *expr_if(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_IF);

  EXPECT(TK_IF);

  // Condition
  expr->left = ambiguous_expr(parser, false);
  if (!expr->left)
    return NULL;

  // Then block
  expr->block = ST_block(parser);

  // Optional else
  if (P_consume(parser, TK_ELSE)) {
    if (CUR->kind == TK_IF) {
      expr->right = expr_if(parser);
    } else {
      expr->right = expr_block(parser);
    }
  }

  return expr;
}

// Parse while expression
static E_Expr *expr_while(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_WHILE);

  EXPECT(TK_WHILE);

  // Condition
  expr->left = ambiguous_expr(parser, false);
  if (!expr->left)
    return NULL;

  // Body block
  expr->block = ST_block(parser);

  return expr;
}

// Parse for loop expression
static E_Expr *expr_for_loop(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_FOR_LOOP);

  EXPECT(TK_FOR);

  // TODO: Parse pattern
  EXPECT(TK_IDENT); // Simplified for now

  EXPECT(TK_IN);

  // Iterator expression
  expr->left = ambiguous_expr(parser, false);
  if (!expr->left)
    return NULL;

  // Body block
  expr->block = ST_block(parser);

  return expr;
}

// Parse loop expression
static E_Expr *expr_loop(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_LOOP);

  EXPECT(TK_LOOP);

  // Body block
  expr->block = ST_block(parser);

  return expr;
}

// Parse match expression
static E_Expr *expr_match(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_MATCH);

  EXPECT(TK_MATCH);

  // Match expression
  expr->left = ambiguous_expr(parser, false);
  if (!expr->left)
    return NULL;

  // TODO: Parse match arms - for now just expect empty braces
  EXPECT(TK_LBRACE);
  EXPECT(TK_RBRACE);

  return expr;
}

// Parse unsafe block
static E_Expr *expr_unsafe(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_UNSAFE);

  EXPECT(TK_UNSAFE);

  // Body block
  expr->block = ST_block(parser);

  return expr;
}

// Parse block expression
static E_Expr *expr_block(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_BLOCK);

  // Body block
  expr->block = ST_block(parser);

  return expr;
}

// Parse range expression
static E_Expr *expr_range(P_Parser *parser, bool allow_struct) {
  E_Expr *expr = enew(parser, NULL, EK_RANGE);

  // Start is empty for ranges starting with ..
  expr->left = NULL;

  if (P_consume(parser, TK_DOTDOT)) {
    expr->range_limits = ERL_HALF_OPEN;
  } else if (P_consume(parser, TK_DOTDOTEQ)) {
    expr->range_limits = ERL_CLOSED;
  }

  // Optional end expression
  if (CUR->kind != TK_SEMI && CUR->kind != TK_RBRACE && CUR->kind != TK_EOF) {
    expr->right = ambiguous_expr(parser, allow_struct);
  }

  return expr;
}

// Parse let expression
static E_Expr *expr_let(P_Parser *parser, bool allow_struct) {
  E_Expr *expr = enew(parser, NULL, EK_LET);

  EXPECT(TK_LET);

  // TODO: Parse pattern (simplified for now)
  EXPECT(TK_IDENT);

  EXPECT(TK_EQ);

  // Right-hand side expression
  expr->right = ambiguous_expr(parser, allow_struct);
  if (!expr->right)
    return NULL;

  return expr;
}

// Parse yield expression
static E_Expr *expr_yield(P_Parser *parser, bool allow_struct) {
  E_Expr *expr = enew(parser, NULL, EK_YIELD);

  EXPECT(TK_YIELD);

  // Optional yield value
  if (CUR->kind != TK_SEMI && CUR->kind != TK_RBRACE && CUR->kind != TK_EOF) {
    expr->left = ambiguous_expr(parser, allow_struct);
  }

  return expr;
}

// Parse const block
static E_Expr *expr_const(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_CONST);

  EXPECT(TK_CONST);

  // Body block
  expr->block = ST_block(parser);

  return expr;
}

// Parse closure expression
static E_Expr *expr_closure(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_CLOSURE);

  // Parse closure parameters: |param1, param2| or ||
  EXPECT(TK_OR);

  // TODO: Parse closure parameters properly
  // For now, just skip to the closing |
  while (CUR->kind != TK_OR && CUR->kind != TK_EOF) {
    P_next(parser);
  }

  EXPECT(TK_OR);

  // Parse closure body (expression or block)
  if (CUR->kind == TK_LBRACE) {
    expr->block = ST_block(parser);
  } else {
    expr->left = ambiguous_expr(parser, true);
  }

  return expr;
}

// Parse async block expression
static E_Expr *expr_async(P_Parser *parser) {
  E_Expr *expr = enew(parser, NULL, EK_ASYNC);

  EXPECT(TK_ASYNC);

  // Parse the block
  expr->block = ST_block(parser);

  return expr;
}



void E_print(P_Printer *printer, const E_Expr *expr) {
  if (!expr) return;

  // Print attributes first
  // TODO: Print attributes when implemented

  switch (expr->kind) {
  case EK_LIT:
    // Literal expression
    P_pushtok(printer, TK_IDENT, expr->loc, expr->literal); // Use IDENT as placeholder
    break;

  case EK_PATH:
    // Path expression
    PT_print_qpath(printer, expr->qself, &expr->path, PTPS_EXPR);
    break;

  case EK_BINARY:
    // Binary expression: left op right
    E_print(printer, expr->left);
    P_pushtok(printer, expr->op, expr->loc, NULL);
    E_print(printer, expr->right);
    break;

  case EK_UNARY:
    // Unary expression: op expr
    P_pushtok(printer, expr->op, expr->loc, NULL);
    E_print(printer, expr->left);
    break;

  case EK_ASSIGN:
    // Assignment expression: left = right
    E_print(printer, expr->left);
    P_pushtok(printer, TK_EQ, expr->loc, NULL);
    E_print(printer, expr->right);
    break;

  case EK_REFERENCE:
    // Reference expression: &[mut] expr
    P_pushtok(printer, TK_AND, expr->loc, NULL);
    if (expr->flags & EML_MUT)
      P_pushtok(printer, TK_MUT, expr->loc, NULL);
    E_print(printer, expr->left);
    break;

  case EK_RAW_ADDR:
    // Raw pointer: &raw [const|mut] expr
    P_pushtok(printer, TK_AND, expr->loc, NULL);
    P_pushtok(printer, TK_IDENT, expr->loc, "raw"); // raw keyword
    if (expr->flags & EML_CONST)
      P_pushtok(printer, TK_CONST, expr->loc, NULL);
    else if (expr->flags & EML_MUT)
      P_pushtok(printer, TK_MUT, expr->loc, NULL);
    E_print(printer, expr->left);
    break;

  case EK_CALL:
    // Function call: func(args...)
    E_print(printer, expr->left);
    P_pushtok(printer, TK_LPAREN, expr->loc, NULL);
    for (size_t i = 0; i < U_len(expr->children); i++) {
      if (i > 0)
        P_pushtok(printer, TK_COMMA, expr->loc, NULL);
      E_print(printer, expr->children[i]);
    }
    P_pushtok(printer, TK_RPAREN, expr->loc, NULL);
    break;

  case EK_METHOD_CALL:
    // Method call: receiver.method(args...)
    E_print(printer, expr->left);
    P_pushtok(printer, TK_DOT, expr->loc, NULL);
    P_pushtok(printer, TK_IDENT, expr->loc, expr->ident);
    P_pushtok(printer, TK_LPAREN, expr->loc, NULL);
    for (size_t i = 0; i < U_len(expr->children); i++) {
      if (i > 0)
        P_pushtok(printer, TK_COMMA, expr->loc, NULL);
      E_print(printer, expr->children[i]);
    }
    P_pushtok(printer, TK_RPAREN, expr->loc, NULL);
    break;

  case EK_FIELD:
    // Field access: expr.field or expr.0
    E_print(printer, expr->left);
    P_pushtok(printer, TK_DOT, expr->loc, NULL);
    if (expr->member.kind == EM_NAMED) {
      P_pushtok(printer, TK_IDENT, expr->loc, expr->member.name);
    } else {
      // Convert index to string
      char index_str[32];
      snprintf(index_str, sizeof(index_str), "%d", expr->member.index);
      P_pushtok(printer, TK_INTEGER, expr->loc, index_str);
    }
    break;

  case EK_INDEX:
    // Array indexing: expr[index]
    E_print(printer, expr->left);
    P_pushtok(printer, TK_LBRACKET, expr->loc, NULL);
    E_print(printer, expr->right);
    P_pushtok(printer, TK_RBRACKET, expr->loc, NULL);
    break;

  case EK_CAST:
    // Cast expression: expr as Type
    E_print(printer, expr->left);
    P_pushtok(printer, TK_AS, expr->loc, NULL);
    TY_print_type(printer, expr->ty);
    break;

  case EK_AWAIT:
    // Await expression: expr.await
    E_print(printer, expr->left);
    P_pushtok(printer, TK_DOT, expr->loc, NULL);
    P_pushtok(printer, TK_AWAIT, expr->loc, NULL);
    break;

  case EK_TRY:
    // Try expression: expr?
    E_print(printer, expr->left);
    P_pushtok(printer, TK_QUESTION, expr->loc, NULL);
    break;

  case EK_PAREN:
    // Parenthesized expression: (expr)
    P_pushtok(printer, TK_LPAREN, expr->loc, NULL);
    E_print(printer, expr->left);
    P_pushtok(printer, TK_RPAREN, expr->loc, NULL);
    break;

  case EK_TUPLE:
    // Tuple expression: (expr1, expr2, ...)
    P_pushtok(printer, TK_LPAREN, expr->loc, NULL);
    for (size_t i = 0; i < U_len(expr->children); i++) {
      if (i > 0)
        P_pushtok(printer, TK_COMMA, expr->loc, NULL);
      E_print(printer, expr->children[i]);
    }
    // Add trailing comma for single-element tuples
    if (U_len(expr->children) == 1)
      P_pushtok(printer, TK_COMMA, expr->loc, NULL);
    P_pushtok(printer, TK_RPAREN, expr->loc, NULL);
    break;

  case EK_ARRAY:
    // Array expression: [expr1, expr2, ...]
    P_pushtok(printer, TK_LBRACKET, expr->loc, NULL);
    for (size_t i = 0; i < U_len(expr->children); i++) {
      if (i > 0)
        P_pushtok(printer, TK_COMMA, expr->loc, NULL);
      E_print(printer, expr->children[i]);
    }
    P_pushtok(printer, TK_RBRACKET, expr->loc, NULL);
    break;

  case EK_REPEAT:
    // Repeat expression: [expr; count]
    P_pushtok(printer, TK_LBRACKET, expr->loc, NULL);
    E_print(printer, expr->left);
    P_pushtok(printer, TK_SEMI, expr->loc, NULL);
    E_print(printer, expr->right);
    P_pushtok(printer, TK_RBRACKET, expr->loc, NULL);
    break;

  case EK_RANGE:
    // Range expression: start..end or start..=end or ..end
    if (expr->left)
      E_print(printer, expr->left);
    if (expr->range_limits == ERL_CLOSED)
      P_pushtok(printer, TK_DOTDOTEQ, expr->loc, NULL);
    else
      P_pushtok(printer, TK_DOTDOT, expr->loc, NULL);
    if (expr->right)
      E_print(printer, expr->right);
    break;

  case EK_IF:
    // If expression: if cond { then } [else { else_block }]
    P_pushtok(printer, TK_IF, expr->loc, NULL);
    E_print(printer, expr->left);
    // TODO: Print block properly when ST_print_block is available
    P_pushtok(printer, TK_LBRACE, expr->loc, NULL);
    P_pushtok(printer, TK_RBRACE, expr->loc, NULL);
    if (expr->right) {
      P_pushtok(printer, TK_ELSE, expr->loc, NULL);
      E_print(printer, expr->right);
    }
    break;

  case EK_WHILE:
    // While expression: while cond { body }
    P_pushtok(printer, TK_WHILE, expr->loc, NULL);
    E_print(printer, expr->left);
    P_pushtok(printer, TK_LBRACE, expr->loc, NULL);
    P_pushtok(printer, TK_RBRACE, expr->loc, NULL);
    break;

  case EK_FOR_LOOP:
    // For loop: for pat in iter { body }
    P_pushtok(printer, TK_FOR, expr->loc, NULL);
    // TODO: Print pattern properly
    P_pushtok(printer, TK_IDENT, expr->loc, "pat");
    P_pushtok(printer, TK_IN, expr->loc, NULL);
    E_print(printer, expr->left);
    P_pushtok(printer, TK_LBRACE, expr->loc, NULL);
    P_pushtok(printer, TK_RBRACE, expr->loc, NULL);
    break;

  case EK_LOOP:
    // Loop expression: loop { body }
    P_pushtok(printer, TK_LOOP, expr->loc, NULL);
    P_pushtok(printer, TK_LBRACE, expr->loc, NULL);
    P_pushtok(printer, TK_RBRACE, expr->loc, NULL);
    break;

  case EK_MATCH:
    // Match expression: match expr { arms }
    P_pushtok(printer, TK_MATCH, expr->loc, NULL);
    E_print(printer, expr->left);
    P_pushtok(printer, TK_LBRACE, expr->loc, NULL);
    P_pushtok(printer, TK_RBRACE, expr->loc, NULL);
    break;

  case EK_BLOCK:
  case EK_UNSAFE:
  case EK_CONST:
  case EK_ASYNC:
    // Block expressions: { stmts }, unsafe { stmts }, const { stmts }, async { stmts }
    if (expr->kind == EK_UNSAFE)
      P_pushtok(printer, TK_UNSAFE, expr->loc, NULL);
    else if (expr->kind == EK_CONST)
      P_pushtok(printer, TK_CONST, expr->loc, NULL);
    else if (expr->kind == EK_ASYNC)
      P_pushtok(printer, TK_ASYNC, expr->loc, NULL);
    P_pushtok(printer, TK_LBRACE, expr->loc, NULL);
    P_pushtok(printer, TK_RBRACE, expr->loc, NULL);
    break;

  case EK_BREAK:
    // Break expression: break [label] [expr]
    P_pushtok(printer, TK_BREAK, expr->loc, NULL);
    if (expr->lifetime) {
      P_pushtok(printer, TK_LIFETIME, expr->loc, expr->lifetime);
    }
    if (expr->left)
      E_print(printer, expr->left);
    break;

  case EK_CONTINUE:
    // Continue expression: continue [label]
    P_pushtok(printer, TK_CONTINUE, expr->loc, NULL);
    if (expr->lifetime) {
      P_pushtok(printer, TK_LIFETIME, expr->loc, expr->lifetime);
    }
    break;

  case EK_RETURN:
    // Return expression: return [expr]
    P_pushtok(printer, TK_RETURN, expr->loc, NULL);
    if (expr->left)
      E_print(printer, expr->left);
    break;

  case EK_YIELD:
    // Yield expression: yield [expr]
    P_pushtok(printer, TK_YIELD, expr->loc, NULL);
    if (expr->left)
      E_print(printer, expr->left);
    break;

  case EK_LET:
    // Let expression: let pat = expr
    P_pushtok(printer, TK_LET, expr->loc, NULL);
    // TODO: Print pattern properly
    P_pushtok(printer, TK_IDENT, expr->loc, "pat");
    P_pushtok(printer, TK_EQ, expr->loc, NULL);
    E_print(printer, expr->right);
    break;

  case EK_INFER:
    // Infer expression: _
    P_pushtok(printer, TK_UNDERSCORE, expr->loc, NULL);
    break;

  case EK_STRUCT:
    // Struct expression: Path { fields }
    PT_print_qpath(printer, expr->qself, &expr->path, PTPS_EXPR);
    P_pushtok(printer, TK_LBRACE, expr->loc, NULL);
    // TODO: Print struct fields
    P_pushtok(printer, TK_RBRACE, expr->loc, NULL);
    break;

  case EK_CLOSURE:
    // Closure expression: |params| body
    P_pushtok(printer, TK_OR, expr->loc, NULL);
    // TODO: Print closure parameters
    P_pushtok(printer, TK_OR, expr->loc, NULL);
    if (expr->block) {
      // Block body
      P_pushtok(printer, TK_LBRACE, expr->loc, NULL);
      P_pushtok(printer, TK_RBRACE, expr->loc, NULL);
    } else if (expr->left) {
      // Expression body
      E_print(printer, expr->left);
    }
    break;

  default:
    // Unknown expression type - just print a placeholder
    P_pushtok(printer, TK_IDENT, expr->loc, "unknown_expr");
    break;
  }
}
