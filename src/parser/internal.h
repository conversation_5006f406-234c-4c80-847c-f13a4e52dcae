// MIT/Apache2 License

#ifndef DOZER_PARSER_INTERNAL_H
#define DOZER_PARSER_INTERNAL_H

#include "../utils/util.h"
#include "parser.h"

#include <stdbool.h>

//
// Tokens (token.c)
//

// Current edition.
typedef enum {
  TE_2015,
  TE_2018,
  TE_2021,
} T_Edition;

// Token printer.
typedef struct P_Printer P_Printer;

// Indicate that an error has occurred.
void T_error(const T_Loc *loc, const char *fmt, ...);

// Convert a token into a keyword token, if need be.
void T_keyword(T_Token *token, T_Edition edition);

// Get a string identifier for the token kind.
const char *T_desc(T_Kind kind);

// Get a string representation of the token.
int T_repr(const T_Token *token, char *buf, size_t len);

//
// Lexer (lexer.c)
//

typedef struct L_Lexer L_Lexer;

// Open a new lexer.
L_Lexer *L_open(const char *sourcefile, T_Edition edition);

// Close a lexer.
void L_close(L_Lexer *lexer);

// Get the next token from a lexer.
void L_lex(L_Lexer *lexer, T_Token *token);

//
// Token Parsing (parser.c)
//

typedef struct P_Parser P_Parser;

// Create a new parser from a lexer.
P_Parser *P_new(L_Lexer *lexer);

// Get the current token.
const T_Token *P_cur(const P_Parser *parser);

// Peek at what the next token is.
const T_Token *P_peek(const P_Parser *parser);

// Peek at what the token after the next token is.
const T_Token *P_peek2(const P_Parser *parser);

// As above, so below, plus one.
const T_Token *P_peek3(const P_Parser *parser);

// Bump to the next token.
void P_next(P_Parser *parser);

// Try to consume a token.
bool P_consume(P_Parser *parser, T_Kind kind);

// Expect a token.
char *P_expect(P_Parser *parser, T_Kind kind);

// Set a parser error.
void P_error(P_Parser *parser, const char *fmt, ...);

// Bail with the current error.
void P_bail(P_Parser *parser);

// Clear the current error.
void P_clear(P_Parser *parser);

// Get the internal arena.
U_Arena *P_arena(const P_Parser *parser);

// Alloc with the parser's internal arena.
void *P_alloc(P_Parser *parser, size_t sz);

// Strdup with the parser's internal arena.
char *P_strdup(P_Parser *parser, const char *str);

// Write a token to a token list.
void P_pushtok(P_Printer *printer, T_Kind kind, T_Loc loc, const char *lit);

// Try to run a parsing function and bail out if it fails.
#define TRY(x)                                                                 \
  do {                                                                         \
    if (!(x))                                                                  \
      return 0;                                                                \
  } while (0)

#define EXPECT(tk)                                                             \
  do {                                                                         \
    if (!P_consume(parser, tk)) {                                              \
      P_error(parser, "expected %s, found %s", T_desc(tk), T_desc(CUR->kind)); \
      return 0;                                                                \
    }                                                                          \
  } while (0)

#define CUR P_cur(parser)
#define PEEK P_peek(parser)

//
// Expression Parsing (expr.c)
//

E_Expr *E_parse(P_Parser *parser);

// Get precedence of a binary operator token
E_Precedence E_precedence_of_binop(T_Kind op);

// Print an expression to tokens
void E_print(P_Printer *printer, const E_Expr *expr);

//
// Path Parsing (path.c)
//

// Parse module-style path.
bool P_modpath(P_Parser *parser, PT_Path *path);
// Read path and QSelf.
bool P_qpath(P_Parser *parser, bool expr_style, PT_Path *path,
             PT_QSelf **qself);

typedef enum PT_PrintStyle {
  PTPS_EXPR,
  PTPS_MOD,
  PTPS_ASWRITTEN
} PT_PrintStyle;

// Convert QSelf and Path to tokens.
void PT_print_qpath(P_Printer *printer, const PT_QSelf *qself,
                    const PT_Path *path, PT_PrintStyle style);

// Check if path is module-style
bool PT_ismodstyle(const PT_Path *path);

//
// Type Parsing (ty.c)
//

// Parse a type
TY_Type *TY_parse(P_Parser *parser);

// Print a type to tokens
void TY_print_type(P_Printer *printer, const TY_Type *type);

//
// Statement Parsing (stmt.c)
//

// Parse a block of statements
ST_Block *ST_block(P_Parser *parser);

#endif
