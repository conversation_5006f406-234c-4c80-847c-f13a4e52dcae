// MIT/Apache2 License

#include "../utils/util.h"
#include "internal.h"

#include <string.h>

// Parse a type
TY_Type *TY_parse(P_Parser *parser) {
  TY_Type *ty;
  
  ty = P_alloc(parser, sizeof(*ty));
  memset(ty, 0, sizeof(*ty));
  
  switch (CUR->kind) {
  case TK_IDENT:
  case TK_PATHSEP:
  case TK_LT:
  case TK_SELFTYPE:
  case TK_SUPER:
  case TK_CRATE:
    // Path type
    ty->kind = TY_PATH;
    if (!P_qpath(parser, false, &ty->path, NULL))
      return NULL;
    break;
  case TK_AND:
    // Reference type
    ty->kind = TY_REFERENCE;
    P_next(parser);
    if (P_consume(parser, TK_MUT))
      ty->flags |= TYIS_MUT;
    ty->inner = TY_parse(parser);
    if (!ty->inner)
      return NULL;
    break;
  case TK_STAR:
    // Pointer type
    ty->kind = TY_PTR;
    P_next(parser);
    if (P_consume(parser, TK_CONST))
      ty->flags |= TYIS_CONST;
    else if (P_consume(parser, TK_MUT))
      ty->flags |= TYIS_MUT;
    ty->inner = TY_parse(parser);
    if (!ty->inner)
      return NULL;
    break;
  case TK_LBRACKET:
    // Array or slice type
    P_next(parser);
    ty->inner = TY_parse(parser);
    if (!ty->inner)
      return NULL;
    
    if (P_consume(parser, TK_SEMI)) {
      // Array type [T; N]
      ty->kind = TY_ARRAY;
      // TODO: Parse array size expression
      U_die("TODO: array size expression");
    } else {
      // Slice type [T]
      ty->kind = TY_SLICE;
    }
    EXPECT(TK_RBRACKET);
    break;
  case TK_LPAREN:
    // Tuple type
    ty->kind = TY_TUPLE;
    P_next(parser);
    
    // Empty tuple
    if (P_consume(parser, TK_RPAREN)) {
      ty->subtypes = U_mkarray(P_arena(parser), sizeof(TY_Type*), 0);
      break;
    }
    
    ty->subtypes = U_mkarray(P_arena(parser), sizeof(TY_Type*), 4);
    
    do {
      TY_Type *elem = TY_parse(parser);
      if (!elem)
        return NULL;
      U_addmem(&ty->subtypes, &elem, 1);
    } while (P_consume(parser, TK_COMMA));
    
    EXPECT(TK_RPAREN);
    break;
  case TK_NOT:
    // Never type
    ty->kind = TY_NEVER;
    P_next(parser);
    break;
  case TK_UNDERSCORE:
    // Infer type
    ty->kind = TY_INFER;
    P_next(parser);
    break;
  default:
    P_error(parser, "expected type, found %s", T_desc(CUR->kind));
    return NULL;
  }
  
  return ty;
}

// Print a type to tokens
void TY_print_type(P_Printer *printer, const TY_Type *type) {
  // TODO: Implement type printing
  (void)printer;
  (void)type;
}
