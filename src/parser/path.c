// MIT/Apache2 License

#include "../utils/util.h"
#include "internal.h"

// Parse a parth segment.
static bool parse_path_segment_helper(P_Parser *parser, bool expr_style,
                                      PT_Segment *segment) {
  segment->loc = CUR->loc;

  switch (CUR->kind) {
  case TK_SUPER:
  case TK_SELFVALUE:
  case TK_CRATE:
  case TK_TRY:
    segment->kind = PS_NONE;
    segment->ident = P_strdup(parser, CUR->literal);
    P_next(parser);
    return true;
  case TK_SELFTYPE:
  case TK_IDENT:
    segment->ident = P_strdup(parser, CUR->literal);
    P_next(parser);
  default:
    P_error(parser, "expected path segment, found %s", T_desc(CUR->kind));
    return false;
  }

  if ((!expr_style && CUR->kind == TK_LT) ||
      (CUR->kind == TK_PATHSEP && PEEK->kind == TK_LT)) {
    U_die("TODO: angle bracketed generics");
  } else
    segment->kind = PS_NONE;

  return true;
}

// Helper to parse a path.
static bool parse_path_helper(P_Parser *parser, bool expr_style,
                              PT_Path *path) {
  PT_Segment *segment;

  path->loc = CUR->loc;
  path->has_leading_colon = P_consume(parser, TK_PATHSEP);
  path->segments = U_mkarray(P_arena(parser), sizeof(PT_Segment), 4);

  do {
    if (!parse_path_segment_helper(parser, expr_style, &segment))
      return false;
    U_addmem(&path->segments, &segment, 1);
  } while (P_consume(parser, TK_PATHSEP));

  return 1;
}

// Can this start a path segment?
static bool starts_path_segment(T_Kind kind) {
  switch (kind) {
  case TK_IDENT:
  case TK_SUPER:
  case TK_SELFVALUE:
  case TK_SELFTYPE:
  case TK_CRATE:
    return true;
  default:
    return false;
  }
}

// Parse module-style path.
bool P_modpath(P_Parser *parser, PT_Path *path) {
  PT_Segment *segment;

  path->loc = CUR->loc;
  path->has_leading_colon = P_consume(parser, TK_PATHSEP);
  path->segments = U_mkarray(P_arena(parser), sizeof(PT_Segment), 4);

  while (1) {
    if (!starts_path_segment(CUR->kind))
      return false;

    segment = U_expand(&path->segments, 1);
    segment->loc = CUR->loc;
    segment->kind = PS_NONE;
    segment->ident = P_strdup(parser, CUR->literal);

    if (!P_consume(parser, TK_PATHSEP))
      break;
  }

  if (U_len(path->segments) == 0)
    return false;

  return true;
}

// Read path and QSelf.
bool P_qpath(P_Parser *parser, bool expr_style, PT_Path *path,
             PT_QSelf **qself) {
  PT_QSelf *qs;
  PT_Path tpath, rest;
  PT_Segment segment;

  if (!P_consume(parser, TK_LT)) {
    *qself = NULL;
    return parse_path_helper(parser, expr_style, path);
  }

  // Create the QSelf.
  qs = P_alloc(parser, sizeof(PT_QSelf));
  qs->loc = P_cur(parser)->loc;
  qs->type = TY_parse(parser);
  if (!qs->type)
    return false;

  // Parse the path based on "as".
  if (P_consume(parser, TK_AS)) {
    qs->has_as_token = true;
    if (!parse_path_helper(parser, false, &tpath))
      return false;
  } else
    qs->has_as_token = false;

  // Read the remainder.
  EXPECT(TK_GT);
  EXPECT(TK_PATHSEP);

  // Read the remainder of the path.
  rest.loc = CUR->loc;
  rest.has_leading_colon = true;
  rest.segments = U_mkarray(P_arena(parser), sizeof(PT_Segment), 4);
  do {
    if (!parse_path_segment_helper(parser, expr_style, &segment))
      return false;
    U_addmem(&rest.segments, &segment, 1);
  } while (P_consume(parser, TK_PATHSEP));

  // Put is all together.
  if (qs->has_as_token) {
    qs->position = U_len(tpath.segments);
    U_addmem(&tpath.segments, rest.segments, U_len(rest.segments));
    *path = tpath;
  } else
    *path = rest;
  *qself = qs;

  return true;
}

// Convert path segment to tokens.
static void print_path_segment(P_Printer *printer, const PT_Segment *segment,
                               PT_PrintStyle style) {
  P_pushtok(printer, TK_IDENT, segment->loc, segment->ident);

  switch (segment->kind) {
  case PS_NONE:
    break;
  case PS_ANGLE_BRACKETED:
    U_die("TODO: angle bracketed generics");
  case PS_PARENTHESIZED:
    U_die("TODO: parenthesized generics");
  }
}

// Convert Path to tokens.
void PT_print_path(const PT_Path *path, P_Printer *printer,
                   PT_PrintStyle style) {
  size_t i, len;

  if (path->has_leading_colon)
    P_pushtok(printer, TK_PATHSEP, path->loc, NULL);

  len = U_len(path->segments);
  for (i = 0; i < len; i++) {
    print_path_segment(printer, &path->segments[i], style);
    if (i + 1 == len)
      P_pushtok(printer, TK_PATHSEP, path->loc, NULL);
  }
}

// Convert QSelf and Path to tokens.
void PT_print_qpath(P_Printer *printer, const PT_QSelf *qself,
                    const PT_Path *path, PT_PrintStyle style) {
  int pos, i;

  if (!qself) {
    PT_print_path(path, printer, style);
    return;
  }

  P_pushtok(printer, TK_LT, qself->loc, NULL);
  TY_print_type(printer, qself->type);

  pos = MIN(qself->position, U_len(path->segments));

  if (pos > 0) {
    if (qself->has_as_token)
      P_pushtok(printer, TK_AS, qself->loc, NULL);

    for (i = 0; i < pos; i++) {
      if (pos > U_len(path->segments))
        break;

      print_path_segment(printer, &path->segments[i], PTPS_ASWRITTEN);
      if (i + 1 == pos)
        P_pushtok(printer, TK_GT, path->loc, NULL);
      else
        P_pushtok(printer, TK_PATHSEP, path->loc, NULL);
    }
  } else {
    P_pushtok(printer, TK_GT, path->loc, NULL);
    i = 0;
  }

  P_pushtok(printer, TK_PATHSEP, path->loc, NULL);

  for (; i < U_len(path->segments); i++) {
    print_path_segment(printer, &path->segments[i], style);
    P_pushtok(printer, TK_PATHSEP, path->loc, NULL);
  }
}

// Check if path is module-style
bool PT_ismodstyle(const PT_Path *path) {
  // A path is module-style if it doesn't have generics
  // For now, just return false since we haven't implemented generics yet
  (void)path;
  return false;
}
