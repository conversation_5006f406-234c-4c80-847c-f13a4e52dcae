// MIT/Apache2 License

#include "../utils/util.h"
#include "internal.h"

#include <string.h>

// Parse a block of statements
ST_Block *ST_block(P_Parser *parser) {
  ST_Block *block = P_alloc(parser, sizeof(ST_Block));
  
  block->loc = CUR->loc;
  block->stmts = U_mkarray(P_arena(parser), sizeof(ST_Stmt), 4);
  
  EXPECT(TK_LBRACE);
  
  // Parse statements until we hit the closing brace
  while (CUR->kind != TK_RBRACE && CUR->kind != TK_EOF) {
    // For now, just parse expressions as statements
    // TODO: Implement proper statement parsing (let bindings, items, etc.)
    E_Expr *expr = E_parse(parser);
    if (!expr)
      break;
      
    ST_Stmt stmt;
    stmt.kind = SK_EXPR;
    stmt.expr = expr;
    U_addmem(&block->stmts, &stmt, 1);
    
    // Optional semicolon
    P_consume(parser, TK_SEMI);
  }
  
  EXPECT(TK_RBRACE);
  return block;
}
