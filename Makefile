# MIT/Apache2 License

.POSIX:
.SUFFIXES: .o .c

PARSEOBJ = src/parser/expr.o src/parser/lex.o src/parser/parser.o \
           src/parser/path.o src/parser/stmt.o src/parser/token.o src/parser/ty.o
OBJS     = src/main.o src/utils/util.o $(PARSEOBJ)

SRC = $(OBJS:.o=.c)

CC     = cc
CFLAGS = -std=c99 -g -Wall -Wextra -Wpedantic

all: dozer
dozer: $(OBJS)
	$(CC) $(LDFLAGS) $(OBJS) -o $@

test_expr: test_expr.o src/utils/util.o $(PARSEOBJ)
	$(CC) $(LDFLAGS) $^ -o $@

.c.o:
	$(CC) $(CFLAGS) -c $< -o $@

$(PARSEOBJ): src/parser/internal.h src/parser/parser.h src/utils/util.h
src/main.o: src/parser/parser.h src/utils/util.h

clean:
	rm -f dozer test_expr $(OBJS) test_expr.o

format:
	clang-format -i $(SRC) src/**/*.h
